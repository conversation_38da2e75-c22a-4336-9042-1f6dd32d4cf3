# DO NOT TOUCH
config-version: 8

# Notifies players with the 'mmoitems.update-notify' perm node when
# they join the server if a new update is available for download.
# Changes apply on server restart.
update-notify: true

# Enable/disable the plugin iterating over the whole player inventory
# instead of just the players armor and held items.
# This option is required for the Ornament item type, however
# it CAN cause lag and/or take a lot of memory.
iterate-whole-inventory: false

# When this is set to true, skins can only be applied to an item ONCE. 
locked-skins: true

# Since 6.9.3 dev builds, MMOItems supports the use of multiple
# RPG core plugins at the same time. However, MMOItems needs one
# specific plugin to hook onto level, class, etc.
#
# Available plugins:
# - MMOCORE (level, class, mana, stamina)
# - HEROES (level, class, mana, stamina)
# - FABLED (level, class, mana)
# - RPGPLAYERLEVELING (level, mana, power)
# - RACESANDCLASSES (level, class, mana)
# - BATTLELEVELS (level)
# - <PERSON>MMO (power level)
# - MCRPG (power level)
# - SK<PERSON><PERSON> or SK<PERSON><PERSON>PRO (class, level, mana)
# - AURELIUM_SKILLS (version 1.X: power level, mana)
# - AURA_SKILLS (version 2+: power level, mana)
preferred-rpg-provider: MMOCORE

# By default, all player inventories will be updated every
# 10 ticks which corresponds to 2 inventory updates a second.
inventory-update-delay: 10

# When generating an item, the item level
# must match approximately the player level
# otherwise the player cannot use items/get useless items.
#
# The item level is always somewhere in the
# interval [playerLevel - spread, playerLevel + spread]
item-level-spread: 2

# When using WorldGuard, these flag checks can sometimes be
# performance expensive. If you are running WorldGuard but are
# not using some of these flags, you can toggle off checks here
# to increase performance.
#
# More information about WorldGuard flags:
# https://gitlab.com/phoenix-dvpmt/mythiclib/-/wikis/********************#flag-plugins
enable_flag_checks:
    commands: true
    weapons: true
    consumables: true
    tools: true

consumables:

    # Toggled off by default. When set to true, right-clicking
    # a consumable will NOT consume it if the player is looking
    # at an interactable block (chests, crafting tables...).
    # Only available in Spigot 1.21+
    disable_clicks_on_blocks: false

durability:

    # Armor durability is deduced as follows: for every 4 pts
    # of damage tanked by the player, one durability point is
    # taken off the item. There is no upper limit on the amount
    # of durability points taken off simultaneously. While this is
    # fine with vanilla Minecraft, when doing super fancy skills that
    # deal tons of damage, armor pieces can be broken in one hit,
    # causing major gameplay problems.
    #
    # This option sets a cap on the amount of durability lost at once.
    # Set it to 0 to disable this option. It is disabled by default
    # to not interfere with the default vanilla behaviour.
    #
    # More info on how many durability points are lost when performing
    # specific actions on the Minecraft Wiki:
    # https://minecraft.fandom.com/wiki/Durability
    loss_cap: 0

# There is currently a client-side Minecraft issue
# where left clicking a
fix-left-click-interact: false

# An item with no tier will use this tier name
# as its default tier name.
default-tier-name: 'Common'

# When an item is generated with no tier,
# this is the capacity formula it will use.
default-item-capacity:
    base: 3
    scale: 0
    spread: 0
    max-spread: 0

# Changes the default value for some stats. This mainly
# accounts for untargeted weapons like staffs, muskets...
default:
    attack-speed: 0.67 # For legacy ranged weapons only (lutes and muskets...). Will be removed in the future
    range: 16
    recoil: 0.1

# Changes apply on server restart.
dropped-items:

    # Items glow based on their tier
    tier-glow: true

    # Display item name over the dropped item
    hints: true

# Some item stats like 'Commands' or NBTTags can be abused to become op.
# You can bind a specific permissions to these stats to limit their edition.
# You need a perm plugin that supports Vault for that option to work.
# The corresponding permission is "mmoitems.edit.op".
op-item-stats:
    enabled: false
    stats:
        - COMMANDS
        - CUSTOM_NBT

item-upgrading:

    # Display name suffix for upgraded items.
    name-suffix: ' &8(&e+#lvl#&8)'

    # Whether to display in Item Name or Lore
    # Disable if item renaming is available to players.
    # If set to 'false', remember to include
    # {upgrade_level} in your item lore.
    display-in-name: true

    # Whether or not to display which
    # stats are changed in the lore.
    display-stat-changes: false
    stat-change-suffix: ' &8(<p>#stat#&8)'
    stat-change-positive: '&a'
    stat-change-negative: '&c'

stats-displaying:

    # This will be a prefix to numeric stats,
    # changing their color when they are undesirable.
    color-positive: ''
    color-negative: ''

    # When items with RNG are displayed in crafting
    # stations, this is the separator between the low
    # and high bound:  Attack Damage  +5⎓7.5
    range-dash: '⎓'

soulbound:

    # Edit soulbound damage when players try to use
    # items which are not bound to themselves.
    damage:
        base: 1
        per-lvl: 1

    # Whether soulbound items should be
    # kept when a player dies.
    keep-on-death: true

    # [Experimental feature]
    # When toggled off, players cannot drop or take
    # Soulbound items away from their inventory.
    # Requires `keep-on-death` enabled.
    # Changes apply on server restart.
    can-drop: true

gem-sockets:

    # Define the text you need to enter in the
    # item gem sockets if you want to create an
    # uncolored gem socket i.e a socket for any type of gem.
    uncolored: 'Uncolored'

custom-blocks:

    # Whether or not custom blocks should
    # generate in the world according to
    # their generation template.
    enable-world-gen: false

    # Whether or not to remove mushroom block drops
    # from the droplist when mining a mushroom block
    # with silk-touch.
    # HIGHLY Recommended for servers that use
    # custom blocks, as the mushroom blocks can BREAK everything.
    replace-mushroom-drops: true

# When set to true, players CANNOT upgrade an item if the
# specs (level, profession requirements) the item WOULD
# have if it were upgraded are too high for the player.
item-upgrade-requirements-check: true

# Players can't use their weapon/abilities when holding
# two-handed item and one other item simultaneously.
# When toggled off, players still receive slow 4.
two-handed-item-restriction: true

# You can select what char will be used to create
# the item ability cooldown progress bar.
cooldown-progress-bar-char: █

# Displays a message on the action bar instead of on the chat.
# Can be used to reduce chat spam.
# Might interfere with other action bar plugins.
action-bar-display:
    ability-cooldown: true
    item-cooldown: true
    not-enough-mana: true
    not-enough-stamina: true
    two-handed: true
    cant-use-item: true
    mitigation: true
    item-break: false

recipes:

    # Enables the vanilla recipe book for MMOItems recipes
    use-recipe-book: true

# "repair" prevents players from repairing MMOItems.
# "smelt" prevents players from smelting MMOItems.
# 'enchant" disables enchanting for MMOItems.
# "craft" prevents players from using MMOItems as ingredients.
# "arrow-shooting" prevents players from shooting any MMOItem that is an arrow.
disable-interactions:
    interact: false
    repair: false
    enchant: false
    smelt: false
    smith: false
    craft: false
    arrow-shooting: false

# Block types that can't be broken by an ability/item effect
block-blacklist:
    - OBSIDIAN

# Allows/disables the permissions needed in order to use items/abilities.
# General permission node for abilities: mmoitems.ability.ability-id
# Ex: - mmoitems.ability.fire-meteor
#     - mmoitems.ability.slow
#     - mmoitems.ability.cursed-fangs
#     etc.
permissions:
    abilities: false
    items: true

#
# When item stats has a numeric range of possibilities.
# attack-damage:
#    base: 10
#    spread: 1
#
# Using the spread, a level, and a random number, we
# obtain a random number (very related to the spread): 1.7
#
# If additive-spread-formula is true, this is added to the base:
#    RESULT: 10 + 1.7  =  11.7 attack damage
#
# If additive-spread-formula is false, this is multiplied to the base:
#    RESULT: 10 * 1.7  =  17 attack damage
#
additive-spread-formula: false

# Disables non MMOItems from being crafted in the vanilla workbench.
# Add [] if the list is empty.
disable-vanilla-recipes: []
# - DIAMOND_PICKAXE
# - IRON_BLOCK

# Options for the Item Revision System
item-revision:

    # Unsocketing gems picks them up with the stats they had
    # when first put into the item, disable this option to
    # force them to be regenerated.
    regenerate-gems-when-unsocketed: false

    # Legacy option to carry tiers over, will take precedence
    # If not specified next to the other keep- flags.
    keep-tiers: true

    # If an item is updated, and the new version does not
    # keep its gems, this will give the gems back to the
    # player so that they don't get lost forever.
    drop-extra-gems: true

    # When keeping lore data, only lore that starts with this
    # will actually be kept.
    kept-lore-prefix: '&7'

    # Whether or not specific stats should be kept
    # when an item is updated to latest revision.
    keep-data:

        # Name of the item, usually renamed via anvil
        display-name: true

        # Enchantments put on the item by the player
        enchantments: true

        # Soulbound applied to the item
        soulbound: true

        # Gems on the item
        gems: true

        # Upgrade level of the item
        upgrades: true

        # Specific lore lines of the item...
        # Warning, this prevents stats in the lore from updating visually!
        lore: false

        # Data that may have been put there by external plugins.
        exsh: true

        # Stats handled by RNG will be rerolled
        reroll: false

        # Modifiers of items ~ Sharp, Light, Heavy, Arcane
        modifications: true

        # Skins applied by MMOItems
        skins: true

        # Tier of the item
        tier: true

        # Third party plugin compatibility
        advanced-enchantments: true

    # Whether specific stats should be kept when gemstones
    # within an item are updated to the latest revision.
    keep-gem-data:

        # Name of the item, usually renamed via anvil
        display-name: true

        # Enchantments put on the item by the player
        enchantments: true

        # Specific lore lines of the item...
        # Warning, this prevents stats in the lore from updating visually!
        lore: false

        # Stats handled by RNG will be rerolled
        reroll: false

    phat-loots:
        display-name: false
        enchantments: false
        soulbound: false
        gems: true
        upgrades: false
        lore: false
        exsh: false
        tier: true
        skins: false
        reroll: true
        modifications: false
        advanced-enchantments: false

    # These will not update at all in PhatLoots, for
    # compatibility with quest plugins that check for
    # exact NBT
    disable-phat-loot:
        - MANGO
        - STEEL_HELMET

    # Here you can disable individual events for when
    # Items should update when a higher revision ID is found
    disable-on:
        pickup: false
        craft: true # 'true' recommended
        click: false
        join: false

# Offset is the distance traveled on X and Y coordinates
# Height is the Y velocity coordinate. Lootsplosions
# only trigger with MythicMobs monsters.
# Changes apply on server restart.
lootsplosion:
    enabled: true
    color: true
    offset: .2
    height: .6

# When Upgrading meets Gems, one must decide if
# gems' stats will increase as well.
gem-upgrade-default: 'NEVER'

# This option allows you to disable the ability to
# use MMOItems that have been removed from your config.
disable-removed-items: true

# Default pickaxe power depending on the item material (for custom blocks only).
# Pickaxe power does not apply to vanilla blocks, only those from MMOItems.
# You may add/remove as many materials as you want in this list.
default-pickaxe-power:
    WOODEN_PICKAXE: 5
    STONE_PICKAXE: 10
    GOLDEN_PICKAXE: 15
    IRON_PICKAXE: 20
    DIAMOND_PICKAXE: 25
    NETHERITE_PICKAXE: 30
    # Add as many as you want.