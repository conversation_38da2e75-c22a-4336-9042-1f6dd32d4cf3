# Add as many custom stats as you want below.
#
# For each custom stat you add, you must also add a corresponding
# entry in the lore-format.yml and stats.yml files.
#
# Item stats are not reloaded when using /mi reload. Make sure
# you restart your server when applying any changes to them.
custom-stats:

  # The key doesn't really matter, although it must be unique
  # The lore for each stat can be defined in language/stats.yml
  1:
    # The name will be used in the edition GUI and also for the stat id
    # Format: "MyLuck" will be replaced with "custom-myluck"
    name: "<PERSON><PERSON>uck"

    # Allowed stats type:
    # - double (numbers)
    # - text
    type: "double"

    # The lore must be a list of strings
    lore:
      - "This is a test line #1"
      - "This is a test line #2"
