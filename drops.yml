# You can create as many subtables as you
# want for each monster/block/mythic mob.
# 
# 'coef' is the subtable coefficient. If a subtable has coef=1 and
# another subtable has coef=3, then the first subtable has 1 (his coefficient)
# chance out of 4 (sum of all subtable coefficients) to be selected.
# The higher the coefficient is, the higher the chance to be the selected subtable.
#
# When a drop table is read, a random subtable is chosen
# among all subtables. It then reads the subtable items.
#
# The subtable names do not matter, just make sure every
# subtable in a drop table has a different name.
#
# Subtable Item Format: [chance to drop],[min amount]-[max amount],[unidentification chance]
#                    or [chance to drop],[amount],[unidentification chance]
# '50,1-3,10' means:
#       - 50% chance of dropping
#       - 1 to 3 items will drop
#       - 10% chance of being unidentified (for each item)

# Blocks Drop Tables
# Read when a block is mined/broken by a player.
blocks:
    DIAMOND_ORE:
        rare-diamond:
            coef: 1
            disable-silk-touch: true
            items:
                MATERIAL:
                    RARE_DIAMOND: 100,2-3,0

# Custom Blocks Drop Tables
# Read when a custom block is mined/broken by a player.
customblocks:
    1:
        more-rare-diamond:
            coef: 1
            disable-silk-touch: true
            items:
                MATERIAL:
                    RARE_DIAMOND: 100,2-3,0

# Monsters Drop Tables
# Read when any monster dies.
monsters:
    BLAZE:
        fire-essence-table:
            coef: 1
            items:
                MATERIAL:
                    fire-essence: 7,1-2,0
    ZOMBIE:
        rock-table:
            coef: 7
            items:
                CONSUMABLE:
                    ROCK: 50,1-3,10
        coin-table:
            coef: 1
            items:
                MISCELLANEOUS:
                    GOLD_COIN: 1,1-10,0
