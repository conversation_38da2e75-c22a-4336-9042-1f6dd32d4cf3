# 'name' is used to display the tier in the item lore.
#
# 'deconstruct-item' is the drop table for the item
# that drops when deconstructing an item.
#
# Tier IDs (TRASH,COMMON...) are case sensitive.

TRASH:
    name: '&8&lTRASH'
    unidentification:
        range: 6
        name: 'Trash'
        prefix: '&8'
    # This tier has no generation options specified.
    # In that case, it'll use the default values set in config.yml
COMMON:
    name: '&7&lCOMMON'
    parent: TRASH

    # Any item with this tier will inherit from this tooltip
    # Has precedence over type-specific tooltip
    # Does not have precedence over item-specific tooltip
    # https://gitlab.com/phoenix-dvpmt/mmoitems/-/wikis/Item-Tooltips
    # This tooltip does not exist
    #tooltip: COMMON

    unidentification:
        range: 6
        name: 'Common'
        prefix: '&7'
    # This tier has no generation options specified.
    # In that case, it'll use the default values set in config.yml
UNCOMMON:

    # Tier name displayed in the item lore. 
    name: '&8&lUNCOMMON'

    #tooltip: UNCOMMON # Does not exist
    
    # Unidentified items display the item tier.
    unidentification:
        
        # Only applies for items with a required
        # levels. Corresponds to the range within
        # which the required level is located.
        range: 6
        
        # Tier name displayed in lore.
        name: 'Uncommon'
        
        # unidentification item name prefix.
        prefix: '&8'
    
    item-glow:
        
        # When enabled, dropped items display
        # an item hint (looks like a hologram).
        hint: true
        
        # Item glow color. 
        # https://htmlcolorcodes.com/bukkit-color-codes/
        # BLACK DARK_BLUE DARK_GREEN DARK_AQUA DARK_RED DARK_PURPLE GOLD GRAY DARK_GRAY BLUE GREEN AQUA RED LIGHT_PURPLE YELLOW WHITE
        color: 'GRAY'
    
    generation:
        chance: 0.15
        capacity:
            base: 6
            
            # Scales on the item level
            scale: .1
            
            spread: .1
            max-spread: .3
    
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    UNCOMMON_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 2
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
RARE:
    name: '&6&lRARE'
    unidentification:
        name: 'Rare'
        range: 6
        prefix: '&6'
    #tooltip: RARE # Does not exist
    generation:
        chance: 0.06
        capacity:
            base: 9
            scale: .15
            spread: .1
            max-spread: .3
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    RARE_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 3
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
VERY_RARE:
    name: '&e&lVERY RARE'
    unidentification:
        name: 'Very Rare'
        prefix: '&e'
        range: 6
    #tooltip: VERY_RARE # Does not exist
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    VERY_RARE_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 3
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
    generation:
        chance: 0.03
        capacity:
            base: 10
            scale: .17
            spread: .1
            max-spread: .3
LEGENDARY:
    name: '&b&lLEGENDARY'
    unidentification:
        name: 'Legendary'
        prefix: '&b'
        range: 6
    #tooltip: LEGENDARY # Does not exist
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    LEGENDARY_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 4
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
    generation:
        chance: 0.01
        capacity:
            base: 12
            scale: .2
            spread: .1
            max-spread: .3
MYTHICAL:
    name: '&5&lMYTHICAL'
    unidentification:
        name: 'Mythical'
        prefix: '&5'
        range: 6
    generation:
        chance: 0.003
        capacity:
            base: 15
            scale: .3
            spread: .1
            max-spread: .3
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    MYTHICAL_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 5
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
EPIC:
    name: '&4&lEPIC'
    unidentification:
        name: 'Epic'
        prefix: '&4'
        range: 6
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    EPIC_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 6
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
    # This tier has no generation options specified.
    # In that case, it'll use the default values set in config.yml
MAGICAL:
    name: '&2&lMAGICAL'
    unidentification:
        name: 'Magical'
        prefix: '&2'
        range: 6
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    MAGICAL_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 3
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
    # This tier has no generation options specified.
    # In that case, it'll use the default values set in config.yml
UNIQUE:
    name: '&c&lUNIQUE'
    unidentification:
        name: 'Unique'
        prefix: '&c'
        range: 6
    deconstruct-item:
        success:
            coef: 1
            items:
                MATERIAL:
                    UNIQUE_WEAPON_ESSENCE: 100,1-1,0
        lose:
            coef: 9
            items:
                MATERIAL:
                    WEAPON_POWDER: 100,1-1,0
    # This tier has no generation options specified.
    # In that case, it'll use the default values set in config.yml
