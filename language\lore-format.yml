# This is the way stats are organized in the item lore.
# You can remove or change line positions as much as you like to.
# Make sure each line ONLY has the placeholder as text.
#
# The lines starting with {bar} are strips that will be
# removed from the lore if there is nothing under it.
# The lines starting with {sbar} will stay whatever is below them.
#
# Reload with /mmoitems reload
lore-format:
  # - '{bar}&8&m--------&f&l &nGeneral&8 &m--------------'
  - '#item-type#'
  - '{bar}'
  - '#required-class#'
  - '#required-level#'
  - '#required-secondary-hero-level#' # Heroes secondary hero level req
  - '#required-dexterity#' # MMOCore attribute requirements
  - '#required-strength#'
  - '#required-intelligence#'
  - '#profession-alchemy#' # MMOCore profession requirements
  - '#profession-enchanting#'
  - '#profession-farming#'
  - '#profession-fishing#'
  - '#profession-mining#'
  - '#profession-smelting#'
  - '#profession-smithing#'
  - '#profession-woodcutting#'
  - '#success-rate#'
  - '{bar}'
  - '#soulbound#'
  # - '{bar}&8&m--------&f&l &nItem Stats&8 &m-------------'
  - '{bar}'
  - '#custom-myluck#'
  - '#can-identify#'
  - '#can-deconstruct#'
  - '#can-deskin#'
  - '#attack-damage#'
  - '#knockback#'
  - '#recoil#'
  - '#note-weight#'
  - '#attack-speed#'
  - '#arrow-velocity#'
  - '#critical-strike-chance#'
  - '#critical-strike-power#'
  - '#skill-critical-strike-chance#'
  - '#skill-critical-strike-power#'
  - '#range#'
  - '#pvp-damage#'
  - '#pve-damage#'
  - '#blunt-power#'
  - '#blunt-rating#'
  - '#weapon-damage#'
  - '#magic-damage#'
  - '#skill-damage#'
  - '#physical-damage#'
  - '#projectile-damage#'
  - '#defense#'
  - '#damage-reduction#'
  - '#fall-damage-reduction#'
  - '#fire-damage-reduction#'
  - '#magic-damage-reduction#'
  - '#projectile-damage-reduction#'
  - '#physical-damage-reduction#'
  - '#pve-damage-reduction#'
  - '#pvp-damage-reduction#'
  - '#undead-damage#'
  - '#faction-damage-undead#'
  - '#block-power#'
  - '#block-rating#'
  - '#dodge-rating#'
  - '#parry-rating#'
  - '#block-cooldown-reduction#'
  - '#dodge-cooldown-reduction#'
  - '#parry-cooldown-reduction#'
  - '#armor#'
  - '#armor-toughness#'
  - '#knockback-resistance#'
  - '#max-health#'
  - '#max-mana#'
  - '#max-stamina#'
  - '#health-regeneration#'
  - '#mana-regeneration#'
  - '#stamina-regeneration#'
  - '#movement-speed#'
  - '#block-break-speed#'
  - '#block-interaction-range#'
  - '#entity-interaction-range#'
  - '#fall-damage-multiplier#'
  - '#gravity#'
  - '#jump-strength#'
  - '#max-absorption#'
  - '#safe-fall-distance#'
  - '#scale#'
  - '#step-height#'
  - '#burning-time#'
  - '#explosion-knockback-resistance#'
  - '#mining-efficiency#'
  - '#movement-efficiency#'
  - '#oxygen-bonus#'
  - '#sneaking-speed#'
  - '#submerged-mining-speed#'
  - '#sweeping-damage-ratio#'
  - '#water-movement-efficiency#'
  - '#lute-attack-effect#'
  - '#two-handed#'
  - '#handworn#'
  - '#autosmelt#'
  - '#bouncing-crack#'
  - '#pickaxe-power#'
  - '#restore-health#'
  - '#restore-food#'
  - '#restore-saturation#'
  - '#restore-mana#'
  - '#restore-stamina#'
  - '#soulbinding-chance#'
  - '#soulbound-break-chance#'
  - '#soulbound-level#'
  - '#repair#'
  - '#repair-percent#'
  - '#item-cooldown#'
  - '#lifesteal#'
  - '#spell-vampirism#'
  - '#additional-dexterity#'
  - '#additional-strength#'
  - '#additional-intelligence#'
  - '#additional-experience#'
  - '#additional-experience-alchemy#' # MMOCore additional profession exp stats
  - '#additional-experience-enchanting#'
  - '#additional-experience-farming#'
  - '#additional-experience-fishing#'
  - '#additional-experience-mining#'
  - '#additional-experience-smelting#'
  - '#additional-experience-smithing#'
  - '#additional-experience-woodcutting#'
  - '#cooldown-reduction#'
  - '#mana-cost#'
  - '#stamina-cost#'
  - '#max-consume#'
  # - '{bar}&8&m--------&2&l &nElements&8 &m--------------'
  - '{bar}'
  - '#elements#'
  # - '{bar}&8&m--------&b&l &nEffects&8 &m---------------'
  - '{bar}'
  - '#effects#'
  # - '{bar}&8&m--------&b&l &nAbilities&8 &m--------------'
  - '{bar}'
  - '#abilities#'
  - '{bar}'
  - '#commands#'
  - '{bar}&8&m--------&b&l &nEffects&8 &m---------------'
  - '#perm-effects#'
  # - '#arrow-potion-effects#'
  # - '{bar}&8&m--------&e&l &nGem Stones&8 &m-----------'
  - '{bar}'
  - '#gem-stones#'
  # - '{sbar}&8&m--------------------------------'
  - '{bar}'
  - '#lore#'
  - '#gem-stone-lore#'
  - '{bar}'
  - '#set#'
  - '{bar}'
  - '#tier#'
  - '#durability#'