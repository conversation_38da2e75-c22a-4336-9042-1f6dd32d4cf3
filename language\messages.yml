received-item: 'You received &6#item#&e#amount#.'
hands-too-charged: 'You can''t do anything, your hands are too charged.'
spell-on-cooldown: '#progress# &eYou must wait #left# second#s# before casting this spell.'
item-on-cooldown: 'This item is on cooldown! Please wait #left# second#s#.'
not-enough-perms-command: 'You don''t have enough permissions.'
not-enough-levels: 'You don''t have enough levels to use this item!'
soulbound-restriction: 'This item is linked to another player, you can''t use it!'
not-enough-perms: 'You don''t have enough permissions to use this.'
wrong-class: 'You don''t have the right class!'
not-enough-mana: 'You don''t have enough mana!'
not-enough-stamina: 'You don''t have enough stamina!'
not-enough-attribute: 'You don''t have enough #attribute#!'
not-enough-profession: 'You don''t have enough levels in #profession#!'
unidentified-item: 'You can''t use an unidentified item!'
zero-durability: 'This item is broken, you first need to repair it.'
cannot-identify-stacked-items: 'You may only identify one item at a time.'
successfully-identified: 'You successfully identified &6#item#&e.'
successfully-deconstructed: 'You successfully deconstructed &6#item#&e.'
gem-stone-applied: 'You successfully applied &6#gem#&e onto your &6#item#&e.'
gem-stone-broke: 'Your gem stone &6#gem#&c broke while trying to apply it onto &6#item#&c.'
repaired-item: 'You successfully repaired &6#item#&e for &6#amount# &euses.'
skin-applied: 'You successfully applied the skin onto your &6#item#&e!'
skin-removed: 'You successfully removed the skin from your &6#item#&e!'
skin-broke: 'Your skin broke while trying to apply it onto your &6#item#&c.'
skin-rejected: 'A skin has already been applied onto your &6#item#&c!'
skin-incompatible: 'This skin is not compatible with your &6#item#&c!'
random-unsocket-gem-too-old: 'The gems have bonded strongly with your item. Cannot remove.'
random-unsocket-success: '&aYou removed &3#gem# &afrom your &6#item#&a!'
cant-bind-item: 'This item is currently linked to #player# by a Lvl #level# soulbound. You will have to break this soulbound first.'
no-soulbound: 'This item is not bound to anyone.'
cant-bind-stacked: 'You can''t bind stacked items.'
unsuccessful-soulbound: 'Your soulbound failed.'
unsuccessful-soulbound-break: 'You couldn''t break the soulbound.'
low-soulbound-level: 'This item soulbound is Lvl #level#. You will need a higher soulbound level on your consumable to break this soulbound.'
successfully-bind-item: 'You successfully applied a Lvl &6#level# &esoulbound to your &6#item#&e.'
successfully-break-bind: 'You successfully broke the Lvl &6#level# &eitem soulbound!'
soulbound-item-lore: '&4Linked to #player#//&4Lvl #level# Soulbound'
cant-upgraded-stack: 'You can''t upgrade stacked items.'
max-upgrades-hit: 'This item cannot be upgraded anymore.'
upgrade-fail: 'Your upgrade failed and you lost your consumable.'
upgrade-fail-station: 'Your upgrade failed and you lost your materials.'
wrong-upgrade-reference: 'You cannot upgrade this item with this consumable.'
upgrade-success: 'You successfully upgraded your &6#item#&e!'
not-have-item-upgrade: 'You don''t have the item to upgrade!'
upgrade-requirement-safe-check: 'You would not meet the upgraded item requirements.'
death-downgrading: '&cYour &6#item#&c got severely damaged that fight...'
not-enough-materials: 'You do not have enough materials to craft this item.'
conditions-not-met: 'You cannot craft this item.'
crafting-canceled: 'You cancelled a crafting recipe.'
crafting-queue-full: 'The crafting queue is currently full.'
unable-to-repair: 'This item can''t be repaired by this consumable!'
