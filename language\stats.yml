# Weapon Stats
attack-damage: '&3 &7➸ Attack Damage: &f{value}'
attack-speed: '&3 &7➸ Attack Speed: &f{value}'
critical-strike-chance: '&3 &7■ Crit Strike Chance: &f<plus>{value}%'
critical-strike-power: '&3 &7■ Crit Strike Power: &f<plus>{value}%'
skill-critical-strike-chance: '&3 &7■ Skill Crit Chance: &f<plus>{value}%'
skill-critical-strike-power: '&3 &7■ Skill Crit Power: &f<plus>{value}%'
range: '&3 &7■ Range: &f{value}' # Combat range for ranged weapons
mana-cost: '&3 &7■ Uses &9{value} Mana'
stamina-cost: '&3 &7■ Uses &9{value} Stamina'
arrow-velocity: '&3 &7■ Arrow Velocity: &f{value}'
blunt-power: '&3 &7■ Blunt Power: &f{value}'
blunt-rating: '&3 &7■ Blunt Rating: &f{value}%'
two-handed: '&3 &7■ Two Handed'
handworn: '&3 &7■ Handworn'
knockback: '&3 &7■ Knockback: &f{value}'
recoil: '&3 &7■ Recoil: &f{value}%'
note-weight: '&3 &7■ Note Weight: &f{value}'
lifesteal: '&3 &7■ Lifesteal: &c+{value}%'
spell-vampirism: '&3 &7■ Spell Vampirism: &c+{value}%'

# Extra Damage
pve-damage: '&3 &7■ PvE Damage: &f<plus>{value}%'
pvp-damage: '&3 &7■ PvP Damage: &f<plus>{value}%'
magic-damage: '&3 &7■ Magic Damage: &f<plus>{value}%'
weapon-damage: '&3 &7■ Weapon Damage: &f<plus>{value}%'
undead-damage: '&3 &7■ Undead Damage: &f<plus>{value}%'
skill-damage: '&3 &7■ Skill Damage: &f<plus>{value}%'
physical-damage: '&3 &7■ Physical Damage: &f<plus>{value}%'
projectile-damage: '&3 &7■ Projectile Damage: &f<plus>{value}%'
faction-damage-undead: '&3 &7■ Undead Faction Damage: &f<plus>{value}%'

# Abilities (changed in MI 6.9.5)
ability:
  general-format: "&8| &7{trigger} &8|&e|&8| &7&l{ability}"
  modifier-if-any: "\n" # Appended to general-format if the skill has at least one modifier
  modifier-foreach: "&7{modifier}&8: &f{value}"
  modifier-splitter: " &8&l|&7 "
  modifiers-per-line: 3
  ability-splitter:
    enabled: true
    format: ''

# Armor Stats
block-power: '&3 &7■ Block Power: &f<plus>{value}%'
block-rating: '&3 &7■ Block Rating: &f<plus>{value}%'
block-cooldown-reduction: '&3 &7■ Block Cooldown Reduction: &f<plus>{value}%'
dodge-rating: '&3 &7■ Dodge Rating: &f<plus>{value}%'
dodge-cooldown-reduction: '&3 &7■ Dodge Cooldown Reduction: &f<plus>{value}%'
parry-rating: '&3 &7■ Parry Rating: &f<plus>{value}%'
parry-cooldown-reduction: '&3 &7■ Parry Cooldown Reduction: &f<plus>{value}%'
armor: '&3 &7✠ Armor: &f<plus>{value}'
armor-toughness: '&3 &7✠ Armor Toughness: &f<plus>{value}'
knockback-resistance: '&3 &7✠ Knockback Resistance: &f<plus>{value}%'
max-health: '&3 &7❤ Health:&c <plus>{value}'
movement-speed: '&3 &7■ Movement Speed: &f<plus>{value}'

# Damage Reduction
defense: '&3 &7■ Defense: &f<plus>{value}'
damage-reduction: '&3 &7■ Damage Reduction: &f<plus>{value}%'
fall-damage-reduction: '&3 &7■ Fall Damage Reduction: &f<plus>{value}%'
fire-damage-reduction: '&3 &7■ Fire Damage Reduction: &f<plus>{value}%'
magic-damage-reduction: '&3 &7■ Magic Damage Reduction: &f<plus>{value}%'
projectile-damage-reduction: '&3 &7■ Projectile Damage Reduction: &f<plus>{value}%'
physical-damage-reduction: '&3 &7■ Physical Damage Reduction: &f<plus>{value}%'
pve-damage-reduction: '&3 &7■ PvE Damage Reduction: &f<plus>{value}%'
pvp-damage-reduction: '&3 &7■ PvP Damage Reduction: &f<plus>{value}%'

# RPG stats
health-regeneration: '&3 &7■ Health Regeneration: &f<plus>{value}'
max-mana: '&3 &7■ Max Mana: &f<plus>{value}'
mana-regeneration: '&3 &7■ Mana Regeneration: &f<plus>{value}'
max-stamina: '&3 &7■ Max Stamina: &f<plus>{value}'
stamina-regeneration: '&3 &7■ Stamina Regeneration: &f<plus>{value}'
cooldown-reduction: '&3 &7■ Skill Cooldown Reduction: &f<plus>{value}%'
additional-experience: '&3 &7■ Additional Experience: &f<plus>{value}%'
additional-experience-alchemy: '&7■ Additional Alchemy Experience: &f<plus>{value}%'
additional-experience-enchanting: '&7■ Additional Enchanting Experience: &f<plus>{value}%'
additional-experience-farming: '&7■ Additional Farming Experience: &f<plus>{value}%'
additional-experience-fishing: '&7■ Additional Fishing Experience: &f<plus>{value}%'
additional-experience-mining: '&7■ Additional Mining Experience: &f<plus>{value}%'
additional-experience-smelting: '&7■ Additional Smelting Experience: &f<plus>{value}%'
additional-experience-smithing: '&7■ Additional Smithing Experience: &f<plus>{value}%'
additional-experience-woodcutting: '&7■ Additional Woodcutting Experience: &f<plus>{value}%'

# 1.20.2+ Attributes
block-break-speed: '&3 &7■ Mining Speed: &f<plus>{value}'
block-interaction-range: '&3 &7■ Range: &f<plus>{value}' # Range for interacting with blocks & mining.
entity-interaction-range: '&3 &7■ Combat Range: &f<plus>{value}'
fall-damage-multiplier: '&3 &7■ Fall Damage: &f<plus>{value}%'
gravity: '&3 &7■ Force of Gravity: &f<plus>{value}%'
jump-strength: '&3 &7■ Jump Strength: &f<plus>{value}'
max-absorption: '&3 &7■ Max Absorption: &f<plus>{value}'
safe-fall-distance: '&3 &7■ Safe Fall Distance: &f<plus>{value}'
scale: '&3 &7■ Size: &f<plus>{value}%'
step-height: '&3 &7■ Smooth Walking: &f<plus>{value}'
burning-time: '&3 &7■ Burning Time: &f<plus>{value}%'
explosion-knockback-resistance: '&3 &7■ Explosion Knockback Resistance: &f<plus>{value}%'
mining-efficiency: '&3 &7■ Mining Efficiency: &f<plus>{value}'
movement-efficiency: '&3 &7■ Movement Efficiency: &f<plus>{value}%'
oxygen-bonus: '&3 &7■ Waterbreathing: &f<plus>{value}'
sneaking-speed: '&3 &7■ Sneaking Speed: &f<plus>{value}%'
submerged-mining-speed: '&3 &7■ Underwater Mining Speed: &f<plus>{value}%'
sweeping-damage-ratio: '&3 &7■ Sweeping Damage: &f<plus>{value}%'
water-movement-efficiency: '&3 &7■ Underwater Movement Speed: &f<plus>{value}%'

# Extra Options
perm-effects: '&3 &7■ Permanent &f{effect}'
commands: '&3 &7■ Command: &f{format} &3 &7(&f{cooldown}&3 &7s)'
item-cooldown: '&3 &7■ &f{value}&3 &7s Cooldown'
arrow-potion-effects: '&3 &7■ Arrow Effect: &f{effect}'

# Consumables
restore-health: '&3 &7■ Restores &f{value} &7Health'
restore-food: '&3 &7■ Restores &f{value} &7Food'
restore-saturation: '&3 &7■ Restores &f{value} &7Saturation'
restore-mana: '&3 &7■ Restores &f{value} &7Mana'
restore-stamina: '&3 &7■ Restores &f{value} &7Stamina'
effects: '&3 &7■ Grants &f{effect} &7for &f{duration} &7s'
repair: '&3 &7■ Repair: &f{value}'
repair-percent: '&3 &7■ Repair: &f{value}%'
can-identify: '&3 &7■ Can identify items.'
can-deconstruct: '&3 &7■ Can deconstruct tiered items.'
can-deskin: '&3 &7■ Can de-skin skinned items.'
success-rate: '&3 &7■ Success Rate: &a&l{value}%'
max-consume: '&3 &7■ &f{value} &7Uses Left'

# Gem Stones
gem-stone-lore: '&8&l[&2&l*&8&l] &aDrag onto an item &7to apply!'
gem-sockets:
  empty: '&a◆ Empty {name} Gem Socket'
  filled: '&a◆ {name}'

# Soulbound
soulbinding-chance: '&3 &7■ Has a &a{value}% &7chance to bind your item.'
soulbound-break-chance: '&3 &7■ Has a &a{value}% &7chance to break soulbounds.'
soulbound-level: '&3 &7■ Level &e{value} &7Soulbound'

# Tools
autosmelt: '&3 &7■ &fAutosmelt'
bouncing-crack: '&3 &7■ &fBouncing Crack'
pickaxe-power: '&3 &7■ &fPickaxe Power: &a{value}'
durability: '&7Durability: {current} / {max}'

# General
item-type: '&c{type}'
tier: '&3 &7Tier: {value}'
required-class: '&3 &7{value} Item'
required-level: '&eRequires Lvl {value}'

# MMOCore Attributes
required-dexterity: '&eRequires &c{value} &eDexterity'
required-strength: '&eRequires &c{value} &eStrength'
required-intelligence: '&eRequires &c{value} &eIntelligence'

additional-dexterity: '&3 &7■ Extra Dexterity: &f<plus>{value}'
additional-strength: '&3 &7■ Extra Strength: &f<plus>{value}'
additional-intelligence: '&3 &7■ Extra Intelligence: &f<plus>{value}'

# MMOCore Professions
profession-alchemy: '&eRequires Alchemy Lvl {value}'
profession-enchanting: '&eRequires Enchanting Lvl {value}'
profession-farming: '&eRequires Farming Lvl {value}'
profession-fishing: '&eRequires Fishing Lvl {value}'
profession-mining: '&eRequires Mining Lvl {value}'
profession-smelting: '&eRequires Smelting Lvl {value}'
profession-smithing: '&eRequires Smithing Lvl {value}'
profession-woodcutting: '&eRequires Woodcutting Lvl {value}'

# Heroes stats
required-secondary-hero-level: '&eRequires 2nd Hero Lvl {value}'

# Elemental Stat Formats
element:
  damage: '{color}{icon}&7 {value} {element} Damage'
  damage-percent: '{color}{icon}&7 +{value}% {element} Damage'
  defense: '{color}{icon}&7 {value} {element} Defense'
  defense-percent: '{color}{icon}&7 +{value}% {element} Defense'
  weakness: '{color}{icon}&7 {value}% {element} Weakness'

# Custom stats
custom-myluck: '&3 &7■ Luck: &f<plus>{value}'
